<script lang="ts">
import { defineComponent, toRefs } from 'vue';

const ComTravelDashboardIndex = defineComponent({
  name: 'ComTravelDashboardIndex',
  components: {},
  props: {
    orderStore: { type: Object, required: true },
    activityStore: { type: Object, required: true },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});

export default ComTravelDashboardIndex;
</script>

<template lang="pug">
.com-travel-dashboard-index.main.flex-1.overflow-y-auto.p-6
  .mb-6
    h1.text-2xl.font-bold.text-gray-800 统计面板

  // Statistics Cards Section
  .bg-white.rounded-lg.shadow.p-6.mb-6(style="overflow: visible")
    .flex.items-center.justify-between.mb-4
      h2.text-lg.font-medium.text-gray-800.flex.items-center
        i.fas.fa-chart-bar.text-blue-500.mr-2
        | 待办统计面板
    // 待办统计面板 - 7个统计数字
    .grid.grid-cols-1.gap-4(class="sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7")
      .metric-card.metric-card-urgent.cursor-pointer
        TaIcon.metric-icon(type="outline/clock")
        .metric-content-top
          .metric-title
            | 24小时急单跟进
          span.tooltip
            TaIcon.tooltip-icon(type="solid/exclamation-circle")
            span.tooltiptext 出行日期为24小时以内
          .metric-description &nbsp;
        .metric-number 0
      .metric-card.metric-card-warning.cursor-pointer
        TaIcon.metric-icon(type="outline/exclamation")
        .metric-content-top
          .metric-title 未收款订单
          .metric-description &nbsp;
        .metric-number 1
      .metric-card.metric-card-info.cursor-pointer
        TaIcon.metric-icon(type="outline/route")
        .metric-content-top
          .metric-title 待跟进行程
          .metric-description &nbsp;
        .metric-number 0
      .metric-card.metric-card-warning.cursor-pointer
        TaIcon.metric-icon(type="outline/user")
        .metric-content-top
          .metric-title 未分配销售订单
          .metric-description &nbsp;
        .metric-number 0
      .metric-card.metric-card-info.cursor-pointer
        TaIcon.metric-icon(type="outline/unlink")
        .metric-content-top
          .metric-title 未关联行程订单
          .metric-description &nbsp;
        .metric-number 0
      .metric-card.metric-card-success.cursor-pointer
        TaIcon.metric-icon(type="outline/calendar")
        .metric-content-top
          .metric-title 本日出行行程
          .metric-description &nbsp;
        .metric-number 0
      .metric-card.metric-card-info.cursor-pointer
        TaIcon.metric-icon(type="outline/calendar")
        .metric-content-top
          .metric-title 次日出行行程
          .metric-description &nbsp;
        .metric-number 0

  // Data Overview Section
  .bg-white.rounded-lg.shadow.p-6.mb-6
    .flex.items-center.justify-between.mb-6
      h2.text-lg.font-medium.text-gray-800.flex.items-center
        i.fas.fa-chart-line.text-red-500.mr-2
        | 数据概览面板
    // Filter Controls Section
    .bg-gray-50.rounded-lg.p-4.mb-6.border.border-gray-200
      .grid.grid-cols-1.gap-4.items-center(class="md:grid-cols-4")
        // 日期类型
        .info
          label.block.text-xs.text-gray-600.mb-1 日期类型
          .relative
            select.w-full.text-sm.border.border-gray-300.rounded.px-3.py-2.appearance-none.bg-white.pr-8(class="focus:border-blue-500 focus:ring-1 focus:ring-blue-500")
              option(value="booking_date") 预订日期
              option(value="travel_date") 出发日期
            .absolute.inset-y-0.right-0.flex.items-center.px-2.pointer-events-none
              i.fas.fa-chevron-down.text-gray-400.text-xs
        // 时间范围
        .info
          label.block.text-xs.text-gray-600.mb-1 时间范围
          .relative
            select.w-full.text-sm.border.border-gray-300.rounded.px-3.py-2.appearance-none.bg-white.pr-8(class="focus:border-blue-500 focus:ring-1 focus:ring-blue-500")
              option(value="yesterday") 昨天
              option(value="7days" selected) 近7天
              option(value="30days") 近30天
              option(value="custom") 自定义
            .absolute.inset-y-0.right-0.flex.items-center.px-2.pointer-events-none
              i.fas.fa-chevron-down.text-gray-400.text-xs
        // 自定义日期范围
        .info(class="md:col-span-2")
          label.block.text-xs.text-gray-600.mb-1 日期范围
          .flex.items-center.space-x-2
            input(type="date").flex-1.text-sm.border.border-gray-300.rounded.px-3.py-2(class="focus:border-blue-500 focus:ring-1 focus:ring-blue-500")
            span.text-gray-500.text-sm -
            input(type="date").flex-1.text-sm.border.border-gray-300.rounded.px-3.py-2(class="focus:border-blue-500 focus:ring-1 focus:ring-blue-500")
      // 分类项筛选
      .mt-6.pt-4.border-t.border-gray-200
        .flex.items-center.justify-between.mb-4
          h3.text-sm.font-semibold.text-gray-700.flex.items-center
            i.fas.fa-filter.text-blue-500.mr-2
            | 分类筛选
          button.text-xs.text-gray-500.transition-colors(@click="clearAllFilters" class="hover:text-gray-700") 清除所有
        .grid.grid-cols-1.gap-4(class="sm:grid-cols-2 lg:grid-cols-4")
          // 销售筛选
          .bg-white.border.border-gray-200.rounded-lg.p-4.transition-all.duration-200.flex.flex-col.items-start(class="hover:border-blue-300")
            .flex.items-center.justify-between.w-full
              label.flex.items-center.cursor-pointer.group
                .relative.flex.items-center.justify-center
                  input.sr-only(id="sales-filter" type="checkbox")
                  .w-4.h-4.border-2.border-gray-300.rounded.bg-white.transition-colors.filter-checkbox.flex.items-center.justify-center(data-target="sales-filter" class="group-hover:border-blue-400")
                    i.fas.fa-check.text-white.text-xs.opacity-0.transition-opacity
                span.ml-3.text-sm.font-medium.text-gray-700(class="group-hover:text-gray-900") 销售
              i.fas.fa-user-tie.text-blue-400.text-sm
            .sales-search.hidden.transition-all.duration-300.mt-3.w-full
              input.w-full.text-sm.border.border-gray-200.rounded-md.px-3.py-2.transition-all(class="focus:border-blue-400.focus:ring-2.focus:ring-blue-100" type="text" placeholder="搜索销售...")
          // 渠道筛选
          .bg-white.border.border-gray-200.rounded-lg.p-4.transition-all.duration-200.flex.flex-col.items-start(class="group-hover:border-blue-400")
            .flex.items-center.justify-between.w-full
              label.flex.items-center.cursor-pointer.group
                .relative.flex.items-center.justify-center
                  input(type="checkbox").sr-only(id="channel-filter")
                  .w-4.h-4.border-2.border-gray-300.rounded.bg-white.transition-colors.filter-checkbox.flex.items-center.justify-center(data-target="channel-filter" class="group-hover:border-blue-400")
                    i.fas.fa-check.text-white.text-xs.opacity-0.transition-opacity
                span.ml-3.text-sm.font-medium.text-gray-700(class="group-hover:text-gray-900") 渠道
              i.fas.fa-route.text-green-400.text-sm
            .channel-search.hidden.transition-all.duration-300.mt-3.w-full
              input.w-full.text-sm.border.border-gray-200.rounded-md.px-3.py-2.transition-all(class="focus:border-blue-400 focus:ring-2 focus:ring-blue-100" type="text" placeholder="搜索订单渠道...")
          //- 收款方式筛选
          .bg-white.border.border-gray-200.rounded-lg.p-4.transition-all.duration-200.flex.flex-col.items-start(class="hover:border-blue-300")
            .flex.items-center.justify-between.w-full
              label.flex.items-center.cursor-pointer.group
                .relative.flex.items-center.justify-center
                  input(type="checkbox").sr-only(id="payment-filter")
                  .w-4.h-4.border-2.border-gray-300.rounded.bg-white.transition-colors.filter-checkbox.flex.items-center.justify-center(data-target="payment-filter" class="group-hover:border-blue-400")
                    i.fas.fa-check.text-white.text-xs.opacity-0.transition-opacity
                span.ml-3.text-sm.font-medium.text-gray-700(class="group-hover:text-gray-900") 收款方式
              i.fas.fa-credit-card.text-purple-400.text-sm
            #payment-search.hidden.transition-all.duration-300.mt-3.w-full
              input.w-full.text-sm.border.border-gray-200.rounded-md.px-3.py-2.transition-all(class="focus:border-blue-400 focus:ring-2 focus:ring-blue-100" type="text" placeholder="搜索收款方式...")
          //- 国家筛选
          .bg-white.border.border-gray-200.rounded-lg.p-4.transition-all.duration-200.flex.flex-col.items-start(class="hover:border-blue-300")
            .flex.items-center.justify-between.w-full
              label.flex.items-center.cursor-pointer.group
                .relative.flex.items-center.justify-center
                  input(type="checkbox").sr-only(id="country-filter")
                  .w-4.h-4.border-2.border-gray-300.rounded.bg-white.transition-colors.filter-checkbox.flex.items-center.justify-center(data-target="country-filter" class="group-hover:border-blue-400")
                    i.fas.fa-check.text-white.text-xs.opacity-0.transition-opacity
                span.ml-3.text-sm.font-medium.text-gray-700(class="group-hover:text-gray-900") 国家
              i.fas.fa-globe.text-orange-400.text-sm
            .country-search.hidden.transition-all.duration-300.mt-3.w-full
              input.w-full.text-sm.border.border-gray-200.rounded-md.px-3.py-2.transition-all(class="focus:border-blue-400 focus:ring-2 focus:ring-blue-100" type="text" placeholder="搜索国家...")
    // Top Metrics Row
    .grid.grid-cols-1.gap-5.mb-6(class="md:grid-cols-2 xl:grid-cols-3")
      // 应收总额
      .bg-white.rounded-2xl.border.border-gray-100.p-6.transition-all.duration-300.group(class="hover:shadow-lg")
        .flex.justify-between.items-start.mb-6
          div
            h3.text-gray-600.text-sm.font-medium.mb-1 应收总额
            .text-xs.text-gray-400 Receivable Amount
          .w-8.h-8.bg-red-50.rounded-lg.flex.items-center.justify-center.transition-colors(class="group-hover:bg-red-100")
            i.fas.fa-arrow-u.text-red-500.text-sm
        .space-y-4
          .text-3xl.font-bold.text-gray-900 ¥15,000
          .flex.items-center.space-x-2
            .flex.items-center.space-x-1
              .w-2.h-2.bg-red-500.rounded-full
              span.text-xs.font-medium.text-red-600 +15%
            span.text-xs.text-gray-500 较前一周期
      // 实收总额
      .bg-white.rounded-2xl.border.border-gray-100.p-6.transition-all.duration-300.group(class="hover:shadow-lg")
        .flex.justify-between.items-start.mb-6
          div
            h3.text-gray-600.text-sm.font-medium.mb-1 实收总额
            .text-xs.text-gray-400 Received Amount
          .w-8.h-8.bg-green-50.rounded-lg.flex.items-center.justify-center.transition-colors(class="group-hover:bg-green-100")
            i.fas.fa-arrow-down.text-green-500.text-sm
        .space-y-4
          .text-3xl.font-bold.text-gray-900 ¥11,250
          .flex.items-center.space-x-2
            .flex.items-center.space-x-1
              .w-2.h-2.bg-green-500.rounded-full
              span.text-xs.font-medium.text-green-600 -12%
            span.text-xs.text-gray-500 较前一周期
      // 收支分布
      .bg-white.rounded-2xl.border.border-gray-100.p-6.transition-all.duration-300.group(class="hover:shadow-lg")
        .flex.justify-between.items-start.mb-6
          div
            h3.text-gray-600.text-sm.font-medium.mb-1 收支分布
            .text-xs.text-gray-400 Revenue Distribution
          .w-8.h-8.bg-purple-50.rounded-lg.flex.items-center.justify-center.transition-colors(class="group-hover:bg-purple-100")
            i.fas.fa-chart-pie.text-purple-500.text-sm
        .flex.items-center.space-x-6
          // 圆环图
          .relative
            .w-20.h-20.relative
              svg.w-20.h-20.transform.-rotate-90(viewBox="0 0 32 32")
                circle(cx="16" cy="16" r="12" fill="none" stroke="#f3f4f6" stroke-width="4")
                circle(cx="16" cy="16" r="12" fill="none" stroke="#10b981" stroke-width="4" stroke-dasharray="56.5 75.4" stroke-linecap="round")
              .absolute.inset-0.flex.items-center.justify-center
                span.text-sm.font-bold.text-gray-700 75%
          // 数据列表
          .flex-1.space-y-3
            .flex.items-center.justify-between
              .flex.items-center.space-x-3
                .w-3.h-3.bg-green-500.rounded-full
                span.text-sm.font-medium.text-gray-700 已收
              span.text-lg.font-bold.text-gray-900 ¥11,250
            .flex.items-center.justify-between
              .flex.items-center.space-x-3
                .w-3.h-3.bg-gray-300.rounded-full
                span.text-sm.font-medium.text-gray-700 未收
              span.text-lg.font-bold.text-gray-900 ¥3,750
</template>

<style lang="stylus" scoped>
.com-travel-manage-notices-index {
  height: 100%;
  width: 100%;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
  position: relative;
  overflow: visible;
  height: 110px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.metric-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 64, 175, 0.12);
  border-color: #e2e8f0;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-content-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.125rem;
  position: relative;
}

.metric-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #64748b;
  text-align: center;
  line-height: 1.2;
  letter-spacing: 0.025em;
  margin-bottom: 0.0625rem;
}

.metric-description {
  font-size: 0.6875rem;
  color: #94a3b8;
  text-align: center;
  line-height: 1.3;
  font-weight: 400;
  margin: 0;
}

.metric-number {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--primary-color);
  text-align: center;
  line-height: 1;
  margin: 0;
}

.metric-card-urgent .metric-number {
  color: #dc2626;
}

.metric-card-warning .metric-number {
  color: #f59e0b;
}

.metric-card-success .metric-number {
  color: #059669;
}

.metric-card-info .metric-number {
  color: #0ea5e9;
}

.metric-icon {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 1.5rem;
  height: 1.5rem;
  opacity: 0.1;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.tooltip-icon {
  display: inline-block;
  margin-left: 0.25rem;
  color: #6b7280;
  font-size: 0.75rem;
  cursor: pointer;
  transition: color 0.3s ease;
  vertical-align: middle;
}

.tooltip-icon:hover {
  color: #4b5563;
}

/* Tooltip Styles */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 140px;
  background-color: #1f2937;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 6px 10px;
  position: absolute;
  z-index: 9999;
  bottom: 150%;
  left: 50%;
  margin-left: -70px;
  opacity: 0;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  font-size: 0.6875rem;
  line-height: 1.3;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  white-space: nowrap;
  pointer-events: none;
}

.tooltip .tooltiptext::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -4px;
  border-width: 4px;
  border-style: solid;
  border-color: #1f2937 transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* Responsive tooltip positioning */
@media (max-width: 768px) {
  .tooltip .tooltiptext {
    width: 120px;
    margin-left: -60px;
    font-size: 0.625rem;
    padding: 5px 8px;
  }
}

/* Ensure tooltip appears above other elements */
.metric-card {
  position: relative;
  z-index: 1;
}

.metric-card:hover {
  z-index: 10;
}

.percentage-positive {
  color: #10b981;
}

.percentage-negative {
  color: #ef4444;
}

.percentage-neutral {
  color: #6b7280;
}

/* Responsive adjustments for metric cards */
@media (max-width: 640px) {
  .metric-card {
    height: 100px;
    padding: 1rem 0.75rem;
  }

  .metric-number {
    font-size: 1.5rem;
  }

  .metric-title {
    font-size: 0.6875rem;
  }

  .metric-content-top {
    min-height: 35px;
  }
}

@media (min-width: 1280px) {
  .metric-card {
    height: 120px;
  }

  .metric-content-top {
    min-height: 45px;
  }
}

/* Enhanced hover effects */
.metric-card-urgent:hover::before {
  background: linear-gradient(90deg, #dc2626, #ef4444);
}

.metric-card-warning:hover::before {
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.metric-card-success:hover::before {
  background: linear-gradient(90deg, #059669, #10b981);
}

.metric-card-info:hover::before {
  background: linear-gradient(90deg, #0ea5e9, #3b82f6);
}

/* Icon color variations */
.metric-card-urgent .metric-icon {
  color: #dc2626;
}

.metric-card-warning .metric-icon {
  color: #f59e0b;
}

.metric-card-success .metric-icon {
  color: #059669;
}

.metric-card-info .metric-icon {
  color: #0ea5e9;
}
</style>
