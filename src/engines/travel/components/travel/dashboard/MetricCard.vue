<template lang="pug">
.metric-card(:class="cardClass" @click="handleClick")
  TaIcon.metric-icon(:type="iconType" :color="iconColor")
  .metric-content-top
    .metric-title
      | {{ title }}
      span.tooltip(v-if="tooltipText")
        TaIcon.tooltip-icon(type="solid/exclamation-circle")
        span.tooltiptext {{ tooltipText }}
    .metric-description &nbsp;
  .metric-number {{ value }}
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

const MetricCard = defineComponent({
  name: 'MetricCard',
  props: {
    title: { type: String, required: true },
    value: { type: [Number, String], required: true },
    iconType: { type: String, required: true },
    cardType: { type: String, default: 'info' },
    tooltipText: { type: String, default: '' },
    clickable: { type: Boolean, default: true },
  },
  emits: ['click'],
  setup(props, { emit }) {
    const cardClass = computed(() => {
      const classes = [`metric-card-${props.cardType}`];
      if (props.clickable) {
        classes.push('cursor-pointer');
      }
      return classes;
    });

    const iconColor = computed(() => {
      const colorMap = {
        urgent: '#dc2626',
        warning: '#f59e0b',
        success: '#059669',
        info: '#0ea5e9',
      };
      return colorMap[props.cardType as keyof typeof colorMap] || '#0ea5e9';
    });

    const handleClick = () => {
      if (props.clickable) {
        emit('click');
      }
    };

    return {
      cardClass,
      iconColor,
      handleClick,
    };
  },
});

export default MetricCard;
</script>

<style scoped lang="stylus">
.metric-card {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
  position: relative;
  overflow: visible;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.metric-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 64, 175, 0.12);
  border-color: #e2e8f0;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    rgba(59, 130, 246, 0.5),
    rgba(147, 51, 234, 0.5)
  );
  border-radius: 12px 12px 0 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-content-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.125rem;
  position: relative;
}

.metric-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  text-align: center;
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.metric-description {
  font-size: 0.625rem;
  color: #9ca3af;
  text-align: center;
}

.metric-number {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  line-height: 1;
}

.metric-card-urgent .metric-number {
  color: #dc2626;
}

.metric-card-warning .metric-number {
  color: #f59e0b;
}

.metric-card-success .metric-number {
  color: #059669;
}

.metric-card-info .metric-number {
  color: #0ea5e9;
}

.metric-icon {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 1.5rem;
  height: 1.5rem;
  opacity: 0.6;
}



/* Tooltip styles */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-icon {
  width: 0.875rem;
  height: 0.875rem;
  color: #6b7280;
  cursor: help;
  margin-left: 0.25rem;
}

.tooltiptext {
  visibility: hidden;
  width: 200px;
  background-color: #1f2937;
  color: white;
  text-align: center;
  border-radius: 6px;
  padding: 8px 12px;
  position: absolute;
  z-index: 1000;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.75rem;
  line-height: 1.4;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #1f2937 transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* Tooltip positioning to avoid affecting card alignment */
.metric-content-top .tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: -25px;
  z-index: 10;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .metric-card {
    height: 100px;
    padding: 1rem 0.75rem;
  }

  .metric-number {
    font-size: 1.5rem;
  }

  .metric-title {
    font-size: 0.6875rem;
  }

  .metric-content-top {
    min-height: 35px;
  }
}

@media (min-width: 1280px) {
  .metric-card {
    height: 120px;
  }

  .metric-content-top {
    min-height: 45px;
  }
}

/* Enhanced hover effects */
.metric-card-urgent:hover::before {
  background: linear-gradient(90deg, #dc2626, #ef4444);
}

.metric-card-warning:hover::before {
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.metric-card-success:hover::before {
  background: linear-gradient(90deg, #059669, #10b981);
}

.metric-card-info:hover::before {
  background: linear-gradient(90deg, #0ea5e9, #3b82f6);
}
</style>
