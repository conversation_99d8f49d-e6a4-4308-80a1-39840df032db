<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>统计面板 - vBooking管理系统</title>
    <link
      href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
    <link rel="stylesheet" href="styles.css" />
    <style>
      :root {
        --primary-color: #1e40af;
        --primary-light: #3b82f6;
        --primary-dark: #1e3a8a;
        --secondary-color: #e0f2fe;
        --text-color: #333;
        --bg-color: #f8f9fa;
        --card-shadow: 0 4px 20px rgba(30, 64, 175, 0.1);
      }

      body {
        font-family: "Segoe UI", "PingFang SC", "Microsoft YaHei", sans-serif;
        background-color: var(--bg-color);
        color: var(--text-color);
      }

      .sidebar {
        background: linear-gradient(
          to bottom,
          var(--primary-color),
          var(--primary-dark)
        );
      }

      .stat-card {
        border-radius: 12px;
        box-shadow: var(--card-shadow);
        transition: all 0.3s ease;
      }

      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(30, 64, 175, 0.15);
      }

      .chart-container {
        border-radius: 12px;
        box-shadow: var(--card-shadow);
      }

      .active-nav-item {
        background-color: rgba(255, 255, 255, 0.1);
        border-left: 4px solid white;
      }

      .nav-item:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }

      .header-search {
        border: 1px solid #e2e8f0;
        border-radius: 50px;
      }

      .header-search:focus-within {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.2);
      }

      .metric-card {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 1px solid #f1f5f9;
        position: relative;
        overflow: visible;
        height: 110px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
      }

      .metric-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(30, 64, 175, 0.12);
        border-color: #e2e8f0;
      }

      .metric-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(
          90deg,
          var(--primary-color),
          var(--primary-light)
        );
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .metric-card:hover::before {
        opacity: 1;
      }

      .metric-content-top {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.125rem;
      }

      .metric-title {
        font-size: 0.75rem;
        font-weight: 600;
        color: #64748b;
        text-align: center;
        line-height: 1.2;
        letter-spacing: 0.025em;
        margin-bottom: 0.0625rem;
      }

      .metric-description {
        font-size: 0.6875rem;
        color: #94a3b8;
        text-align: center;
        line-height: 1.3;
        font-weight: 400;
        margin: 0;
      }

      .metric-number {
        font-size: 1.875rem;
        font-weight: 700;
        color: var(--primary-color);
        text-align: center;
        line-height: 1;
        margin: 0;
      }

      .metric-card-urgent .metric-number {
        color: #dc2626;
      }

      .metric-card-warning .metric-number {
        color: #f59e0b;
      }

      .metric-card-success .metric-number {
        color: #059669;
      }

      .metric-card-info .metric-number {
        color: #0ea5e9;
      }

      .metric-icon {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        width: 1.5rem;
        height: 1.5rem;
        opacity: 0.1;
        color: var(--primary-color);
        font-size: 1.25rem;
      }

      .tooltip-icon {
        display: inline-block;
        margin-left: 0.25rem;
        color: #6b7280;
        font-size: 0.75rem;
        cursor: pointer;
        transition: color 0.3s ease;
        vertical-align: middle;
      }

      .tooltip-icon:hover {
        color: #4b5563;
      }

      /* Tooltip Styles */
      .tooltip {
        position: relative;
        display: inline-block;
      }

      .tooltip .tooltiptext {
        visibility: hidden;
        width: 140px;
        background-color: #1f2937;
        color: #fff;
        text-align: center;
        border-radius: 6px;
        padding: 6px 10px;
        position: absolute;
        z-index: 9999;
        bottom: 150%;
        left: 50%;
        margin-left: -70px;
        opacity: 0;
        transition: opacity 0.3s ease, visibility 0.3s ease;
        font-size: 0.6875rem;
        line-height: 1.3;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
        white-space: nowrap;
        pointer-events: none;
      }

      .tooltip .tooltiptext::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -4px;
        border-width: 4px;
        border-style: solid;
        border-color: #1f2937 transparent transparent transparent;
      }

      .tooltip:hover .tooltiptext {
        visibility: visible;
        opacity: 1;
      }

      /* Responsive tooltip positioning */
      @media (max-width: 768px) {
        .tooltip .tooltiptext {
          width: 120px;
          margin-left: -60px;
          font-size: 0.625rem;
          padding: 5px 8px;
        }
      }

      /* Ensure tooltip appears above other elements */
      .metric-card {
        position: relative;
        z-index: 1;
      }

      .metric-card:hover {
        z-index: 10;
      }

      .percentage-positive {
        color: #10b981;
      }

      .percentage-negative {
        color: #ef4444;
      }

      .percentage-neutral {
        color: #6b7280;
      }

      /* Responsive adjustments for metric cards */
      @media (max-width: 640px) {
        .metric-card {
          height: 100px;
          padding: 1rem 0.75rem;
        }

        .metric-number {
          font-size: 1.5rem;
        }

        .metric-title {
          font-size: 0.6875rem;
        }

        .metric-content-top {
          min-height: 35px;
        }
      }

      @media (min-width: 1280px) {
        .metric-card {
          height: 120px;
        }

        .metric-content-top {
          min-height: 45px;
        }
      }

      /* Enhanced hover effects */
      .metric-card-urgent:hover::before {
        background: linear-gradient(90deg, #dc2626, #ef4444);
      }

      .metric-card-warning:hover::before {
        background: linear-gradient(90deg, #f59e0b, #fbbf24);
      }

      .metric-card-success:hover::before {
        background: linear-gradient(90deg, #059669, #10b981);
      }

      .metric-card-info:hover::before {
        background: linear-gradient(90deg, #0ea5e9, #3b82f6);
      }

      /* Icon color variations */
      .metric-card-urgent .metric-icon {
        color: #dc2626;
      }

      .metric-card-warning .metric-icon {
        color: #f59e0b;
      }

      .metric-card-success .metric-icon {
        color: #059669;
      }

      .metric-card-info .metric-icon {
        color: #0ea5e9;
      }
    </style>
  </head>
  <body class="h-screen flex overflow-hidden">
    <!-- Sidebar -->
    <aside class="sidebar text-white w-64 flex-shrink-0 hidden md:block">
      <div class="p-4 h-16 flex items-center">
        <h1 class="text-xl font-bold flex items-center">
          <span class="text-2xl mr-2">📊</span> vBooking
        </h1>
      </div>
      <nav class="mt-5">
        <a
          href="dashboard.html"
          class="nav-item flex items-center py-3 px-4 hover:bg-opacity-30 transition"
        >
          <i class="fas fa-home w-5 h-5 mr-3"></i>
          <span>首页</span>
        </a>
        <a
          href="product-list.html"
          class="nav-item flex items-center py-3 px-4 hover:bg-opacity-30 transition"
        >
          <i class="fas fa-suitcase w-5 h-5 mr-3"></i>
          <span>产品管理</span>
        </a>
        <a
          href="order-list.html"
          class="nav-item flex items-center py-3 px-4 hover:bg-opacity-30 transition"
        >
          <i class="fas fa-shopping-cart w-5 h-5 mr-3"></i>
          <span>订单管理</span>
        </a>
        <a
          href="statistics.html"
          class="active-nav-item flex items-center py-3 px-4"
        >
          <i class="fas fa-chart-pie w-5 h-5 mr-3"></i>
          <span>统计分析</span>
        </a>
        <a
          href="#"
          class="nav-item flex items-center py-3 px-4 hover:bg-opacity-30 transition"
        >
          <i class="fas fa-users w-5 h-5 mr-3"></i>
          <span>客户管理</span>
        </a>
        <a
          href="#"
          class="nav-item flex items-center py-3 px-4 hover:bg-opacity-30 transition"
        >
          <i class="fas fa-bullhorn w-5 h-5 mr-3"></i>
          <span>营销活动</span>
        </a>
        <a
          href="#"
          class="nav-item flex items-center py-3 px-4 hover:bg-opacity-30 transition"
        >
          <i class="fas fa-comments w-5 h-5 mr-3"></i>
          <span>客服对话</span>
        </a>
        <div class="mt-6"></div>
        <a
          href="#"
          class="nav-item flex items-center py-3 px-4 hover:bg-opacity-30 transition"
        >
          <i class="fas fa-cog w-5 h-5 mr-3"></i>
          <span>设置</span>
        </a>
        <a
          href="#"
          class="nav-item flex items-center py-3 px-4 hover:bg-opacity-30 transition"
        >
          <i class="fas fa-question-circle w-5 h-5 mr-3"></i>
          <span>帮助中心</span>
        </a>
      </nav>
    </aside>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top Navbar -->
      <header class="bg-white shadow-sm z-10">
        <div class="flex items-center justify-between h-16 px-6">
          <!-- Mobile menu button -->
          <button class="md:hidden text-gray-500 focus:outline-none">
            <i class="fas fa-bars"></i>
          </button>

          <!-- Search Bar -->
          <div class="relative w-64 md:w-96">
            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
              <i class="fas fa-search text-gray-400"></i>
            </span>
            <input
              type="text"
              class="header-search w-full pl-10 pr-4 py-2 focus:outline-none"
              placeholder="搜索客户、产品等..."
            />
          </div>

          <!-- User Menu -->
          <div class="flex items-center">
            <button
              class="flex items-center text-gray-500 mx-4 focus:outline-none relative"
            >
              <i class="fas fa-bell text-xl"></i>
              <span
                class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"
              ></span>
            </button>
            <div class="relative ml-3 flex items-center">
              <img
                class="h-8 w-8 rounded-full object-cover"
                src="https://randomuser.me/api/portraits/men/32.jpg"
                alt="User avatar"
              />
              <div class="ml-2">
                <p class="text-sm font-medium">管理员</p>
                <p class="text-xs text-gray-500">系统管理员</p>
              </div>
              <button class="ml-2 text-gray-400">
                <i class="fas fa-ellipsis-v"></i>
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="flex-1 overflow-y-auto p-6">
        <!-- Page Header -->
        <div class="mb-6">
          <h1 class="text-2xl font-bold text-gray-800">统计面板</h1>
        </div>

        <!-- Statistics Cards Section -->
        <div
          class="bg-white rounded-lg shadow p-6 mb-6"
          style="overflow: visible"
        >
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-800 flex items-center">
              <i class="fas fa-chart-bar text-blue-500 mr-2"></i>
              待办统计面板
            </h2>
          </div>

          <!-- 待办统计面板 - 7个统计数字 -->
          <div
            class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4"
          >
            <div class="metric-card metric-card-urgent cursor-pointer" onclick="openDrawer('urgent', '24小时急单跟进', 0)">
              <i class="fas fa-clock metric-icon"></i>
              <div class="metric-content-top">
                <p class="metric-title">
                  24小时急单跟进
                  <span class="tooltip">
                    <i class="fas fa-exclamation-circle tooltip-icon"></i>
                    <span class="tooltiptext">出行日期为24小时以内</span>
                  </span>
                </p>
                <p class="metric-description">&nbsp;</p>
              </div>
              <p class="metric-number">0</p>
            </div>
            <div class="metric-card metric-card-warning cursor-pointer" onclick="openDrawer('unpaid', '未收款订单', 1)">
              <i class="fas fa-exclamation-triangle metric-icon"></i>
              <div class="metric-content-top">
                <p class="metric-title">未收款订单</p>
                <p class="metric-description">&nbsp;</p>
              </div>
              <p class="metric-number">1</p>
            </div>
            <div class="metric-card metric-card-info cursor-pointer" onclick="openDrawer('pending', '待跟进行程', 0)">
              <i class="fas fa-route metric-icon"></i>
              <div class="metric-content-top">
                <p class="metric-title">待跟进行程</p>
                <p class="metric-description">&nbsp;</p>
              </div>
              <p class="metric-number">0</p>
            </div>
            <div class="metric-card metric-card-warning cursor-pointer" onclick="openDrawer('unassigned', '未分配销售订单', 0)">
              <i class="fas fa-user-times metric-icon"></i>
              <div class="metric-content-top">
                <p class="metric-title">未分配销售订单</p>
                <p class="metric-description">&nbsp;</p>
              </div>
              <p class="metric-number">0</p>
            </div>
            <div class="metric-card metric-card-info cursor-pointer" onclick="openDrawer('unlinked', '未关联行程订单', 0)">
              <i class="fas fa-unlink metric-icon"></i>
              <div class="metric-content-top">
                <p class="metric-title">未关联行程订单</p>
                <p class="metric-description">&nbsp;</p>
              </div>
              <p class="metric-number">0</p>
            </div>
            <div class="metric-card metric-card-success cursor-pointer" onclick="openDrawer('today', '本日出行行程', 0)">
              <i class="fas fa-calendar-day metric-icon"></i>
              <div class="metric-content-top">
                <p class="metric-title">本日出行行程</p>
                <p class="metric-description">&nbsp;</p>
              </div>
              <p class="metric-number">0</p>
            </div>
            <div class="metric-card metric-card-info cursor-pointer" onclick="openDrawer('tomorrow', '次日出行行程', 0)">
              <i class="fas fa-calendar-plus metric-icon"></i>
              <div class="metric-content-top">
                <p class="metric-title">次日出行行程</p>
                <p class="metric-description">&nbsp;</p>
              </div>
              <p class="metric-number">0</p>
            </div>
          </div>
        </div>

        <!-- Data Overview Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-medium text-gray-800 flex items-center">
              <i class="fas fa-chart-line text-red-500 mr-2"></i>
              数据概览面板
            </h2>
          </div>

          <!-- Filter Controls Section - Moved above metrics -->
          <div class="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
              <!-- 日期类型 -->
              <div>
                <label class="block text-xs text-gray-600 mb-1">日期类型</label>
                <div class="relative">
                  <select
                    class="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 appearance-none bg-white pr-8"
                  >
                    <option value="booking_date">预订日期</option>
                    <option value="travel_date">出发日期</option>
                  </select>
                  <div
                    class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
                  >
                    <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                  </div>
                </div>
                <div
                  class="mt-2 bg-white border border-gray-200 rounded shadow-lg hidden"
                  id="dateTypeDropdown"
                >
                  <div class="py-1">
                    <div
                      class="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                      onclick="selectDateType('booking_date', '预订日期')"
                    >
                      预订日期
                    </div>
                    <div
                      class="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                      onclick="selectDateType('travel_date', '出发日期')"
                    >
                      出发日期
                    </div>
                  </div>
                </div>
              </div>

              <!-- 时间范围 -->
              <div>
                <label class="block text-xs text-gray-600 mb-1">时间范围</label>
                <div class="relative">
                  <select
                    class="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 appearance-none bg-white pr-8"
                  >
                    <option value="yesterday">昨天</option>
                    <option value="7days" selected>近7天</option>
                    <option value="30days">近30天</option>
                    <option value="custom">自定义</option>
                  </select>
                  <div
                    class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
                  >
                    <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                  </div>
                </div>
                <div
                  class="mt-2 bg-white border border-gray-200 rounded shadow-lg hidden"
                  id="timeRangeDropdown"
                >
                  <div class="py-1">
                    <div
                      class="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                      onclick="selectTimeRange('yesterday', '昨天')"
                    >
                      昨天
                    </div>
                    <div
                      class="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                      onclick="selectTimeRange('7days', '近7天')"
                    >
                      近7天
                    </div>
                    <div
                      class="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                      onclick="selectTimeRange('30days', '近30天')"
                    >
                      近30天
                    </div>
                    <div
                      class="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                      onclick="selectTimeRange('custom', '自定义')"
                    >
                      自定义
                    </div>
                  </div>
                </div>
              </div>

              <!-- 自定义日期范围 -->
              <div class="md:col-span-2">
                <label class="block text-xs text-gray-600 mb-1">日期范围</label>
                <div class="flex items-center space-x-2">
                  <input
                    type="date"
                    class="flex-1 text-sm border border-gray-300 rounded px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    value="2025-06-22"
                  />
                  <span class="text-gray-500 text-sm">-</span>
                  <input
                    type="date"
                    class="flex-1 text-sm border border-gray-300 rounded px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    value="2025-06-22"
                  />
                </div>
              </div>
            </div>

            <!-- 分类项筛选 -->
            <div class="mt-6 pt-4 border-t border-gray-200">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-sm font-semibold text-gray-700 flex items-center">
                  <i class="fas fa-filter text-blue-500 mr-2"></i>
                  分类筛选
                </h3>
                <button class="text-xs text-gray-500 hover:text-gray-700 transition-colors" onclick="clearAllFilters()">
                  清除所有
                </button>
              </div>
              
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- 销售筛选 -->
                <div class="bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-all duration-200 flex flex-col items-start">
                  <div class="flex items-center justify-between w-full">
                    <label class="flex items-center cursor-pointer group">
                      <div class="relative flex items-center justify-center">
                        <input type="checkbox" class="sr-only" id="sales-filter" />
                        <div class="w-4 h-4 border-2 border-gray-300 rounded bg-white group-hover:border-blue-400 transition-colors filter-checkbox flex items-center justify-center" data-target="sales-filter">
                          <i class="fas fa-check text-white text-xs opacity-0 transition-opacity"></i>
                        </div>
                      </div>
                      <span class="ml-3 text-sm font-medium text-gray-700 group-hover:text-gray-900">销售</span>
                    </label>
                    <i class="fas fa-user-tie text-blue-400 text-sm"></i>
                  </div>
                  <div class="hidden transition-all duration-300 mt-3 w-full" id="sales-search">
                    <input
                      type="text"
                      placeholder="搜索销售..."
                      class="w-full text-sm border border-gray-200 rounded-md px-3 py-2 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all"
                    />
                  </div>
                </div>

                <!-- 渠道筛选 -->
                <div class="bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-all duration-200 flex flex-col items-start">
                  <div class="flex items-center justify-between w-full">
                    <label class="flex items-center cursor-pointer group">
                      <div class="relative flex items-center justify-center">
                        <input type="checkbox" class="sr-only" id="channel-filter" />
                        <div class="w-4 h-4 border-2 border-gray-300 rounded bg-white group-hover:border-blue-400 transition-colors filter-checkbox flex items-center justify-center" data-target="channel-filter">
                          <i class="fas fa-check text-white text-xs opacity-0 transition-opacity"></i>
                        </div>
                      </div>
                      <span class="ml-3 text-sm font-medium text-gray-700 group-hover:text-gray-900">渠道</span>
                    </label>
                    <i class="fas fa-route text-green-400 text-sm"></i>
                  </div>
                  <div class="hidden transition-all duration-300 mt-3 w-full" id="channel-search">
                    <input
                      type="text"
                      placeholder="搜索订单渠道..."
                      class="w-full text-sm border border-gray-200 rounded-md px-3 py-2 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all"
                    />
                  </div>
                </div>

                <!-- 收款方式筛选 -->
                <div class="bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-all duration-200 flex flex-col items-start">
                  <div class="flex items-center justify-between w-full">
                    <label class="flex items-center cursor-pointer group">
                      <div class="relative flex items-center justify-center">
                        <input type="checkbox" class="sr-only" id="payment-filter" />
                        <div class="w-4 h-4 border-2 border-gray-300 rounded bg-white group-hover:border-blue-400 transition-colors filter-checkbox flex items-center justify-center" data-target="payment-filter">
                          <i class="fas fa-check text-white text-xs opacity-0 transition-opacity"></i>
                        </div>
                      </div>
                      <span class="ml-3 text-sm font-medium text-gray-700 group-hover:text-gray-900">收款方式</span>
                    </label>
                    <i class="fas fa-credit-card text-purple-400 text-sm"></i>
                  </div>
                  <div class="hidden transition-all duration-300 mt-3 w-full" id="payment-search">
                    <input
                      type="text"
                      placeholder="搜索收款方式..."
                      class="w-full text-sm border border-gray-200 rounded-md px-3 py-2 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all"
                    />
                  </div>
                </div>

                <!-- 国家筛选 -->
                <div class="bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-all duration-200 flex flex-col items-start">
                  <div class="flex items-center justify-between w-full">
                    <label class="flex items-center cursor-pointer group">
                      <div class="relative flex items-center justify-center">
                        <input type="checkbox" class="sr-only" id="country-filter" />
                        <div class="w-4 h-4 border-2 border-gray-300 rounded bg-white group-hover:border-blue-400 transition-colors filter-checkbox flex items-center justify-center" data-target="country-filter">
                          <i class="fas fa-check text-white text-xs opacity-0 transition-opacity"></i>
                        </div>
                      </div>
                      <span class="ml-3 text-sm font-medium text-gray-700 group-hover:text-gray-900">国家</span>
                    </label>
                    <i class="fas fa-globe text-orange-400 text-sm"></i>
                  </div>
                  <div class="hidden transition-all duration-300 mt-3 w-full" id="country-search">
                    <input
                      type="text"
                      placeholder="搜索国家..."
                      class="w-full text-sm border border-gray-200 rounded-md px-3 py-2 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Top Metrics Row - Reference Style -->
          <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5 mb-6">
            <!-- 应收总额 -->
            <div class="bg-white rounded-2xl border border-gray-100 p-6 hover:shadow-lg transition-all duration-300 group">
              <div class="flex justify-between items-start mb-6">
                <div>
                  <h3 class="text-gray-600 text-sm font-medium mb-1">应收总额</h3>
                  <p class="text-xs text-gray-400">Receivable Amount</p>
                </div>
                <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center group-hover:bg-red-100 transition-colors">
                  <i class="fas fa-arrow-up text-red-500 text-sm"></i>
                </div>
              </div>
              
              <div class="space-y-4">
                <div class="text-3xl font-bold text-gray-900">¥15,000</div>
                <div class="flex items-center space-x-2">
                  <div class="flex items-center space-x-1">
                    <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span class="text-xs font-medium text-red-600">+15%</span>
                  </div>
                  <span class="text-xs text-gray-500">较前一周期</span>
                </div>
              </div>
            </div>

            <!-- 实收总额 -->
            <div class="bg-white rounded-2xl border border-gray-100 p-6 hover:shadow-lg transition-all duration-300 group">
              <div class="flex justify-between items-start mb-6">
                <div>
                  <h3 class="text-gray-600 text-sm font-medium mb-1">实收总额</h3>
                  <p class="text-xs text-gray-400">Received Amount</p>
                </div>
                <div class="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center group-hover:bg-green-100 transition-colors">
                  <i class="fas fa-arrow-down text-green-500 text-sm"></i>
                </div>
              </div>
              
              <div class="space-y-4">
                <div class="text-3xl font-bold text-gray-900">¥11,250</div>
                <div class="flex items-center space-x-2">
                  <div class="flex items-center space-x-1">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-xs font-medium text-green-600">-12%</span>
                  </div>
                  <span class="text-xs text-gray-500">较前一周期</span>
                </div>
              </div>
            </div>

            <!-- 收支分布 -->
            <div class="bg-white rounded-2xl border border-gray-100 p-6 hover:shadow-lg transition-all duration-300 group">
              <div class="flex justify-between items-start mb-6">
                <div>
                  <h3 class="text-gray-600 text-sm font-medium mb-1">收支分布</h3>
                  <p class="text-xs text-gray-400">Revenue Distribution</p>
                </div>
                <div class="w-8 h-8 bg-purple-50 rounded-lg flex items-center justify-center group-hover:bg-purple-100 transition-colors">
                  <i class="fas fa-chart-pie text-purple-500 text-sm"></i>
                </div>
              </div>
              
              <div class="flex items-center space-x-6">
                <!-- 圆环图 -->
                <div class="relative">
                  <div class="w-20 h-20 relative">
                    <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 32 32">
                      <circle cx="16" cy="16" r="12" fill="none" stroke="#f3f4f6" stroke-width="4"></circle>
                      <circle cx="16" cy="16" r="12" fill="none" stroke="#10b981" stroke-width="4"
                              stroke-dasharray="56.5 75.4" stroke-linecap="round"></circle>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                      <span class="text-sm font-bold text-gray-700">75%</span>
                    </div>
                  </div>
                </div>

                <!-- 数据列表 -->
                <div class="flex-1 space-y-3">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span class="text-sm font-medium text-gray-700">已收</span>
                    </div>
                    <span class="text-lg font-bold text-gray-900">¥11,250</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <div class="w-3 h-3 bg-gray-300 rounded-full"></div>
                      <span class="text-sm font-medium text-gray-700">未收</span>
                    </div>
                    <span class="text-lg font-bold text-gray-900">¥3,750</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>



      </main>
    </div>

    <!-- Right Drawer -->
    <div id="drawer-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300"></div>
    <div id="drawer" class="fixed top-0 right-0 h-full w-96 bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
      <div class="flex flex-col h-full">
        <!-- Drawer Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h3 id="drawer-title" class="text-lg font-semibold text-gray-900"></h3>
            <p id="drawer-count" class="text-sm text-gray-500 mt-1"></p>
          </div>
          <button onclick="closeDrawer()" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <i class="fas fa-times text-gray-500"></i>
          </button>
        </div>

        <!-- Drawer Content -->
        <div class="flex-1 overflow-y-auto p-6">
          <div id="drawer-content">
            <!-- Content will be dynamically loaded here -->
          </div>
        </div>
      </div>
    </div>

    <script>
      // Current date and time
      const today = new Date();
      const options = {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      };

      // Add interactive features
      document.addEventListener("DOMContentLoaded", function () {
        // Add hover effects to metric cards
        const metricCards = document.querySelectorAll(".metric-card");
        metricCards.forEach((card) => {
          card.addEventListener("mouseenter", function () {
            this.style.transform = "translateY(-2px)";
          });
          card.addEventListener("mouseleave", function () {
            this.style.transform = "translateY(0)";
          });
        });

        // Category filter functionality with custom checkboxes
        const filterCategories = [
          { checkbox: 'sales-filter', search: 'sales-search', color: 'blue' },
          { checkbox: 'channel-filter', search: 'channel-search', color: 'blue' },
          { checkbox: 'payment-filter', search: 'payment-search', color: 'blue' },
          { checkbox: 'country-filter', search: 'country-search', color: 'blue' }
        ];

        // Handle custom checkbox styling and functionality
        document.querySelectorAll('.filter-checkbox').forEach(checkboxDiv => {
          const targetId = checkboxDiv.getAttribute('data-target');
          const checkbox = document.getElementById(targetId);
          const searchDiv = document.getElementById(targetId.replace('-filter', '-search'));
          const checkIcon = checkboxDiv.querySelector('i');
          const color = getColorForFilter(targetId);
          
          // Function to update visual state
          function updateCheckboxState(isChecked) {
            if (isChecked) {
              // Show checked state
              checkboxDiv.classList.add(`border-${color}-500`, `bg-${color}-500`);
              checkboxDiv.classList.remove('border-gray-300', 'bg-white');
              checkIcon.classList.remove('opacity-0');
              checkIcon.classList.add('opacity-100');
              
              // Show search input
              if (searchDiv) {
                searchDiv.classList.remove('hidden');
                searchDiv.style.display = 'block';
              }
            } else {
              // Show unchecked state
              checkboxDiv.classList.remove(`border-${color}-500`, `bg-${color}-500`);
              checkboxDiv.classList.add('border-gray-300', 'bg-white');
              checkIcon.classList.add('opacity-0');
              checkIcon.classList.remove('opacity-100');
              
              // Hide search input
              if (searchDiv) {
                searchDiv.classList.add('hidden');
                searchDiv.style.display = 'none';
              }
            }
          }
          
          // Click handler for checkbox div
          checkboxDiv.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Toggle checkbox state
            checkbox.checked = !checkbox.checked;
            updateCheckboxState(checkbox.checked);
            
            // Trigger change event
            checkbox.dispatchEvent(new Event('change'));
          });
          
          // Click handler for the label (to handle clicks on text)
          const label = checkboxDiv.closest('.bg-white').querySelector('label');
          if (label) {
            label.addEventListener('click', function(e) {
              e.preventDefault();
              e.stopPropagation();
              
              // Toggle checkbox state
              checkbox.checked = !checkbox.checked;
              updateCheckboxState(checkbox.checked);
              
              // Trigger change event
              checkbox.dispatchEvent(new Event('change'));
            });
          }
          
          // Listen for programmatic changes to checkbox
          checkbox.addEventListener('change', function() {
            updateCheckboxState(this.checked);
          });
        });

        // Helper function to get color for filter
        function getColorForFilter(filterId) {
          const colorMap = {
            'sales-filter': 'blue',
            'channel-filter': 'blue',
            'payment-filter': 'blue',
            'country-filter': 'blue'
          };
          return colorMap[filterId] || 'blue';
        }

        // Clear all filters function
        window.clearAllFilters = function() {
          filterCategories.forEach(category => {
            const checkbox = document.getElementById(category.checkbox);
            const searchDiv = document.getElementById(category.search);
            const checkboxDiv = document.querySelector(`[data-target="${category.checkbox}"]`);
            const checkIcon = checkboxDiv.querySelector('i');
            
            if (checkbox && checkbox.checked) {
              checkbox.checked = false;
              
              // Reset visual state
              checkboxDiv.classList.remove('border-' + category.color + '-500', 'bg-' + category.color + '-500');
              checkboxDiv.classList.add('border-gray-300', 'bg-white');
              checkIcon.classList.add('opacity-0');
              checkIcon.classList.remove('opacity-100');
              
              // Hide search input
              if (searchDiv) {
                searchDiv.classList.add('hidden');
                searchDiv.style.display = 'none';
                // Clear search input value
                const searchInput = searchDiv.querySelector('input');
                if (searchInput) searchInput.value = '';
              }
            }
          });
        };

        // Simulate real-time data updates
        setInterval(function () {
          const randomCards = document.querySelectorAll(
            ".metric-card .text-2xl, .metric-card .text-lg"
          );
          randomCards.forEach((card) => {
            if (card.textContent !== "-" && card.textContent !== "--") {
              // Add subtle animation to indicate data refresh
              card.style.opacity = "0.7";
              setTimeout(() => {
                card.style.opacity = "1";
              }, 200);
            }
          });
        }, 30000); // Update every 30 seconds

        // Drawer functionality
        window.openDrawer = function(type, title, count) {
          const drawer = document.getElementById('drawer');
          const overlay = document.getElementById('drawer-overlay');
          const drawerTitle = document.getElementById('drawer-title');
          const drawerCount = document.getElementById('drawer-count');
          const drawerContent = document.getElementById('drawer-content');

          // Set title and count
          drawerTitle.textContent = title;
          drawerCount.textContent = `共 ${count} 条记录`;

          // Generate content based on type
          drawerContent.innerHTML = generateDrawerContent(type, count);

          // Show drawer
          overlay.classList.remove('hidden');
          drawer.classList.remove('translate-x-full');
          document.body.style.overflow = 'hidden'; // Prevent background scrolling
        };

        window.closeDrawer = function() {
          const drawer = document.getElementById('drawer');
          const overlay = document.getElementById('drawer-overlay');

          drawer.classList.add('translate-x-full');
          overlay.classList.add('hidden');
          document.body.style.overflow = ''; // Restore scrolling
        };

        // Close drawer when clicking overlay
        document.getElementById('drawer-overlay').addEventListener('click', closeDrawer);

        function generateDrawerContent(type, count) {
          if (count === 0) {
            return `
              <div class="text-center py-12">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                  <i class="fas fa-inbox text-gray-400 text-2xl"></i>
                </div>
                <h4 class="text-lg font-medium text-gray-900 mb-2">暂无数据</h4>
                <p class="text-gray-500">当前没有需要处理的项目</p>
              </div>
            `;
          }

          // Sample data for demonstration
          const sampleData = {
            urgent: [
              { id: 'ORD001', customer: '张三', destination: '北京', departureDate: '2025-07-05', amount: '¥15,800' }
            ],
            unpaid: [
              { id: 'ORD002', customer: '李四', destination: '上海', departureDate: '2025-07-08', amount: '¥12,500' }
            ],
            pending: [],
            unassigned: [],
            unlinked: [],
            today: [],
            tomorrow: []
          };

          const data = sampleData[type] || [];
          
          if (data.length === 0) {
            return generateDrawerContent('empty', 0);
          }

          let content = '<div class="space-y-4">';
          
          data.forEach((item, index) => {
            content += `
              <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors cursor-pointer">
                <div class="flex justify-between items-start mb-3">
                  <div class="font-medium text-gray-900">${item.id}</div>
                  <div class="text-sm font-semibold text-blue-600">${item.amount}</div>
                </div>
                <div class="grid grid-cols-2 gap-2 text-sm text-gray-600">
                  <div>
                    <span class="text-gray-500">客户：</span>${item.customer}
                  </div>
                  <div>
                    <span class="text-gray-500">目的地：</span>${item.destination}
                  </div>
                  <div class="col-span-2">
                    <span class="text-gray-500">出发日期：</span>${item.departureDate}
                  </div>
                </div>
                <div class="mt-3 flex space-x-2">
                  <button class="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors">
                    查看详情
                  </button>
                  <button class="px-3 py-1 bg-gray-200 text-gray-700 text-xs rounded hover:bg-gray-300 transition-colors">
                    处理
                  </button>
                </div>
              </div>
            `;
          });

          content += '</div>';
          return content;
        }
      });
    </script>
  </body>
</html>
